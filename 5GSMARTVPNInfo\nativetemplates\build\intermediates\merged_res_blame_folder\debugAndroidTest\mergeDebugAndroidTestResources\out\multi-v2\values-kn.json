{"logs": [{"outputFile": "com.google.android.ads.nativetemplates.test.nativetemplates-mergeDebugAndroidTestResources-18:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\95c0856ee9f8851848d96b463dd25e7c\\transformed\\play-services-base-18.0.0\\res\\values-kn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,466,595,706,845,970,1074,1169,1315,1424,1585,1716,1857,2010,2075,2134", "endColumns": "106,165,128,110,138,124,103,94,145,108,160,130,140,152,64,58,80", "endOffsets": "299,465,594,705,844,969,1073,1168,1314,1423,1584,1715,1856,2009,2074,2133,2214"}, "to": {"startLines": "29,30,31,32,33,34,35,36,38,39,40,41,42,43,44,45,46", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2859,2970,3140,3273,3388,3531,3660,3768,4013,4163,4276,4441,4576,4721,4878,4947,5010", "endColumns": "110,169,132,114,142,128,107,98,149,112,164,134,144,156,68,62,84", "endOffsets": "2965,3135,3268,3383,3526,3655,3763,3862,4158,4271,4436,4571,4716,4873,4942,5005,5090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9e5a086bc9443d69ab2fe958744d7d0e\\transformed\\appcompat-1.2.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,919,1010,1103,1198,1292,1392,1485,1580,1674,1765,1856,1938,2054,2164,2263,2376,2481,2595,2759,2859", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,527,634,760,838,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1933,2049,2159,2258,2371,2476,2590,2754,2854,2937"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,919,1010,1103,1198,1292,1392,1485,1580,1674,1765,1856,1938,2054,2164,2263,2376,2481,2595,2759,7163", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,527,634,760,838,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1933,2049,2159,2258,2371,2476,2590,2754,2854,7241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4174daf14328b785510b24290d95f19a\\transformed\\play-services-ads-24.3.0\\res\\values-kn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,245,294,351,417,486,594,658,790,913,1050,1101,1152,1274,1371,1414,1506,1542,1579,1630,1708,1752", "endColumns": "45,48,56,65,68,107,63,131,122,136,50,50,121,96,42,91,35,36,50,77,43,55", "endOffsets": "244,293,350,416,485,593,657,789,912,1049,1100,1151,1273,1370,1413,1505,1541,1578,1629,1707,1751,1807"}, "to": {"startLines": "51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5526,5576,5629,5690,5760,5833,5945,6013,6149,6276,6417,6472,6527,6653,6754,6801,6897,6937,6978,7033,7115,7347", "endColumns": "49,52,60,69,72,111,67,135,126,140,54,54,125,100,46,95,39,40,54,81,47,59", "endOffsets": "5571,5624,5685,5755,5828,5940,6008,6144,6271,6412,6467,6522,6648,6749,6796,6892,6932,6973,7028,7110,7158,7402"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1aae7574d5734885cc339c3642ba94ef\\transformed\\play-services-basement-18.4.0\\res\\values-kn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "37", "startColumns": "4", "startOffsets": "3867", "endColumns": "145", "endOffsets": "4008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b1798530a3a51669983781b25eadf6b2\\transformed\\browser-1.8.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,269,385", "endColumns": "113,99,115,100", "endOffsets": "164,264,380,481"}, "to": {"startLines": "47,48,49,50", "startColumns": "4,4,4,4", "startOffsets": "5095,5209,5309,5425", "endColumns": "113,99,115,100", "endOffsets": "5204,5304,5420,5521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ffe8fb42c4c871da1b77a11883e42fba\\transformed\\core-1.8.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "7246", "endColumns": "100", "endOffsets": "7342"}}]}]}
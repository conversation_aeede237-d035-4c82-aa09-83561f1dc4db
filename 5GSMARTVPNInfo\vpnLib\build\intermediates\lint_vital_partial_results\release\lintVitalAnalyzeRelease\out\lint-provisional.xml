<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.10.1" type="conditional_incidents">

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="14579"
                    endOffset="14580"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="14579"
                    endOffset="14580"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="370"
            column="80"
            startOffset="14579"
            endLine="370"
            endColumn="81"
            endOffset="14580"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="19428"
                    endOffset="19429"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="19428"
                    endOffset="19429"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="495"
            column="89"
            startOffset="19428"
            endLine="495"
            endColumn="90"
            endOffset="19429"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="20113"
                    endOffset="20114"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="20113"
                    endOffset="20114"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="508"
            column="80"
            startOffset="20113"
            endLine="508"
            endColumn="81"
            endOffset="20114"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="20644"
                    endOffset="20645"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="20644"
                    endOffset="20645"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="518"
            column="80"
            startOffset="20644"
            endLine="518"
            endColumn="81"
            endOffset="20645"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="21168"
                    endOffset="21169"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="21168"
                    endOffset="21169"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="530"
            column="77"
            startOffset="21168"
            endLine="530"
            endColumn="78"
            endOffset="21169"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="57224"
                    endOffset="57225"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
                    startOffset="57224"
                    endOffset="57225"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNService.java"
            line="1443"
            column="76"
            startOffset="57224"
            endLine="1443"
            endColumn="77"
            endOffset="57225"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNServiceV2.java"
                    startOffset="22535"
                    endOffset="22536"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNServiceV2.java"
                    startOffset="22535"
                    endOffset="22536"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNServiceV2.java"
            line="612"
            column="88"
            startOffset="22535"
            endLine="612"
            endColumn="89"
            endOffset="22536"/>
    </incident>

    <incident
        id="UnspecifiedImmutableFlag"
        severity="fatal"
        message="Missing `PendingIntent` mutability flag">
        <fix-alternatives>
            <fix-replace
                description="Add FLAG_IMMUTABLE (preferred)"
                replacement="android.app.PendingIntent.FLAG_IMMUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNServiceV2.java"
                    startOffset="23663"
                    endOffset="23664"/>
            </fix-replace>
            <fix-replace
                description="Add FLAG_MUTABLE"
                replacement="android.app.PendingIntent.FLAG_MUTABLE"
                shortenNames="true"
                reformat="value"
                priority="0">
                <range
                    file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNServiceV2.java"
                    startOffset="23663"
                    endOffset="23664"/>
            </fix-replace>
        </fix-alternatives>
        <location
            file="${:vpnLib*release*MAIN*sourceProvider*0*javaDir*0}/de/blinkt/openvpn/core/OpenVPNServiceV2.java"
            line="635"
            column="99"
            startOffset="23663"
            endLine="635"
            endColumn="100"
            endOffset="23664"/>
    </incident>

</incidents>

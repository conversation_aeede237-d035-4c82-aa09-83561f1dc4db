1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="de.blinkt.openvpn.test" >
4
5    <uses-sdk
5-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9697554076483648275.xml:5:5-74
6        android:minSdkVersion="21"
6-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9697554076483648275.xml:5:15-41
7        android:targetSdkVersion="34" />
7-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9697554076483648275.xml:5:42-71
8
9    <instrumentation
9-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9697554076483648275.xml:11:5-15:72
10        android:name="androidx.test.runner.AndroidJUnitRunner"
10-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9697554076483648275.xml:11:22-76
11        android:functionalTest="false"
11-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9697554076483648275.xml:14:22-52
12        android:handleProfiling="false"
12-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9697554076483648275.xml:13:22-53
13        android:label="Tests for de.blinkt.openvpn.test"
13-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9697554076483648275.xml:15:22-70
14        android:targetPackage="de.blinkt.openvpn.test" />
14-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9697554076483648275.xml:12:22-68
15
16    <queries>
16-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f8c5f5f8089e48711eee93d1b3e5cf7\transformed\runner-1.6.1\AndroidManifest.xml:24:5-28:15
17        <package android:name="androidx.test.orchestrator" />
17-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f8c5f5f8089e48711eee93d1b3e5cf7\transformed\runner-1.6.1\AndroidManifest.xml:25:9-62
17-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f8c5f5f8089e48711eee93d1b3e5cf7\transformed\runner-1.6.1\AndroidManifest.xml:25:18-59
18        <package android:name="androidx.test.services" />
18-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f8c5f5f8089e48711eee93d1b3e5cf7\transformed\runner-1.6.1\AndroidManifest.xml:26:9-58
18-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f8c5f5f8089e48711eee93d1b3e5cf7\transformed\runner-1.6.1\AndroidManifest.xml:26:18-55
19        <package android:name="com.google.android.apps.common.testing.services" />
19-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f8c5f5f8089e48711eee93d1b3e5cf7\transformed\runner-1.6.1\AndroidManifest.xml:27:9-83
19-->[androidx.test:runner:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6f8c5f5f8089e48711eee93d1b3e5cf7\transformed\runner-1.6.1\AndroidManifest.xml:27:18-80
20    </queries>
21
22    <uses-permission android:name="android.permission.REORDER_TASKS" />
22-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:24:5-72
22-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:24:22-69
23    <uses-permission android:name="android.permission.WAKE_LOCK" />
23-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:5-68
23-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:23:22-65
24    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
24-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:5-79
24-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:24:22-76
25    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
25-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:5-81
25-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:25:22-78
26    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
26-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:5-77
26-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:26:22-74
27
28    <permission
28-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\658890c3eb29411b5be4b265d2b74ea2\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
29        android:name="de.blinkt.openvpn.test.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
29-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\658890c3eb29411b5be4b265d2b74ea2\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
30        android:protectionLevel="signature" />
30-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\658890c3eb29411b5be4b265d2b74ea2\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
31
32    <uses-permission android:name="de.blinkt.openvpn.test.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
32-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\658890c3eb29411b5be4b265d2b74ea2\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
32-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\658890c3eb29411b5be4b265d2b74ea2\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
33
34    <application
34-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9697554076483648275.xml:7:5-9:19
35        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
35-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\658890c3eb29411b5be4b265d2b74ea2\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
36        android:debuggable="true"
37        android:extractNativeLibs="true" >
38        <uses-library android:name="android.test.runner" />
38-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9697554076483648275.xml:8:9-60
38-->C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\tmp\manifest\androidTest\debug\tempFile1ProcessTestManifest9697554076483648275.xml:8:23-57
39
40        <service
40-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:19
41            android:name="de.blinkt.openvpn.core.OpenVPNService"
41-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-65
42            android:exported="true"
42-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
43            android:foregroundServiceType="dataSync"
43-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-53
44            android:permission="android.permission.BIND_VPN_SERVICE" >
44-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-69
45            <intent-filter>
45-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:29
46                <action android:name="android.net.VpnService" />
46-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-65
46-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:25-62
47            </intent-filter>
48        </service> <!-- VpnService-based OpenVPN implementation (V2) -->
49        <service
49-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-27:19
50            android:name="de.blinkt.openvpn.core.OpenVPNServiceV2"
50-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-67
51            android:exported="true"
51-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
52            android:foregroundServiceType="dataSync"
52-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-53
53            android:permission="android.permission.BIND_VPN_SERVICE" >
53-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-69
54            <intent-filter>
54-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:29
55                <action android:name="android.net.VpnService" />
55-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-65
55-->[:vpnLib] C:\xampp\htdocs\Svpn5g\5GSMARTVPNInfo\vpnLib\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:25-62
56            </intent-filter>
57        </service>
58
59        <activity
59-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:27:9-34:20
60            android:name="androidx.test.core.app.InstrumentationActivityInvoker$BootstrapActivity"
60-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:28:13-99
61            android:exported="true"
61-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:29:13-36
62            android:theme="@style/WhiteBackgroundTheme" >
62-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:30:13-56
63            <intent-filter android:priority="-100" >
63-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:31:13-33:29
63-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:31:28-51
64                <category android:name="android.intent.category.LAUNCHER" />
64-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:32:17-77
64-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:32:27-74
65            </intent-filter>
66        </activity>
67        <activity
67-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:35:9-42:20
68            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyActivity"
68-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:36:13-95
69            android:exported="true"
69-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:37:13-36
70            android:theme="@style/WhiteBackgroundTheme" >
70-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:38:13-56
71            <intent-filter android:priority="-100" >
71-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:31:13-33:29
71-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:31:28-51
72                <category android:name="android.intent.category.LAUNCHER" />
72-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:32:17-77
72-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:32:27-74
73            </intent-filter>
74        </activity>
75        <activity
75-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:43:9-50:20
76            android:name="androidx.test.core.app.InstrumentationActivityInvoker$EmptyFloatingActivity"
76-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:44:13-103
77            android:exported="true"
77-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:45:13-36
78            android:theme="@style/WhiteBackgroundDialogTheme" >
78-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:46:13-62
79            <intent-filter android:priority="-100" >
79-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:31:13-33:29
79-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:31:28-51
80                <category android:name="android.intent.category.LAUNCHER" />
80-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:32:17-77
80-->[androidx.test:core:1.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3279aaee18be2b468358634ea5987dd4\transformed\core-1.6.1\AndroidManifest.xml:32:27-74
81            </intent-filter>
82        </activity>
83
84        <provider
84-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:29:9-37:20
85            android:name="androidx.startup.InitializationProvider"
85-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:30:13-67
86            android:authorities="de.blinkt.openvpn.test.androidx-startup"
86-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:31:13-68
87            android:exported="false" >
87-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:32:13-37
88            <meta-data
88-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:34:13-36:52
89                android:name="androidx.work.WorkManagerInitializer"
89-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:35:17-68
90                android:value="androidx.startup" />
90-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:36:17-49
91            <meta-data
91-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c1868e2fbf1ecc0125468b7c6a9b846\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
92                android:name="androidx.emoji2.text.EmojiCompatInitializer"
92-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c1868e2fbf1ecc0125468b7c6a9b846\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
93                android:value="androidx.startup" />
93-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7c1868e2fbf1ecc0125468b7c6a9b846\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
94            <meta-data
94-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ff754083e4944205cd923ca05677106\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
95                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
95-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ff754083e4944205cd923ca05677106\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
96                android:value="androidx.startup" />
96-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3ff754083e4944205cd923ca05677106\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
97            <meta-data
97-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
98                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
99                android:value="androidx.startup" />
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
100        </provider>
101
102        <service
102-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:39:9-45:35
103            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
103-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:40:13-88
104            android:directBootAware="false"
104-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:41:13-44
105            android:enabled="@bool/enable_system_alarm_service_default"
105-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:42:13-72
106            android:exported="false" />
106-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:43:13-37
107        <service
107-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:46:9-52:35
108            android:name="androidx.work.impl.background.systemjob.SystemJobService"
108-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:47:13-84
109            android:directBootAware="false"
109-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:48:13-44
110            android:enabled="@bool/enable_system_job_service_default"
110-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:49:13-70
111            android:exported="true"
111-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:50:13-36
112            android:permission="android.permission.BIND_JOB_SERVICE" />
112-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:51:13-69
113        <service
113-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:53:9-59:35
114            android:name="androidx.work.impl.foreground.SystemForegroundService"
114-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:54:13-81
115            android:directBootAware="false"
115-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:55:13-44
116            android:enabled="@bool/enable_system_foreground_service_default"
116-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:56:13-77
117            android:exported="false" />
117-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:57:13-37
118
119        <receiver
119-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:61:9-66:35
120            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
120-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:62:13-88
121            android:directBootAware="false"
121-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:63:13-44
122            android:enabled="true"
122-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:64:13-35
123            android:exported="false" />
123-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:65:13-37
124        <receiver
124-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:67:9-77:20
125            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
125-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:68:13-106
126            android:directBootAware="false"
126-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:69:13-44
127            android:enabled="false"
127-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:70:13-36
128            android:exported="false" >
128-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:71:13-37
129            <intent-filter>
129-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:73:13-76:29
130                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
130-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:17-87
130-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:74:25-84
131                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
131-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:17-90
131-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:75:25-87
132            </intent-filter>
133        </receiver>
134        <receiver
134-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:78:9-88:20
135            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
135-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:79:13-104
136            android:directBootAware="false"
136-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:80:13-44
137            android:enabled="false"
137-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:81:13-36
138            android:exported="false" >
138-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:82:13-37
139            <intent-filter>
139-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:84:13-87:29
140                <action android:name="android.intent.action.BATTERY_OKAY" />
140-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:17-77
140-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:85:25-74
141                <action android:name="android.intent.action.BATTERY_LOW" />
141-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:17-76
141-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:86:25-73
142            </intent-filter>
143        </receiver>
144        <receiver
144-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:89:9-99:20
145            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
145-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:90:13-104
146            android:directBootAware="false"
146-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:91:13-44
147            android:enabled="false"
147-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:92:13-36
148            android:exported="false" >
148-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:93:13-37
149            <intent-filter>
149-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:95:13-98:29
150                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
150-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:17-83
150-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:96:25-80
151                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
151-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:17-82
151-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:97:25-79
152            </intent-filter>
153        </receiver>
154        <receiver
154-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:100:9-109:20
155            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
155-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:101:13-103
156            android:directBootAware="false"
156-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:102:13-44
157            android:enabled="false"
157-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:103:13-36
158            android:exported="false" >
158-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:104:13-37
159            <intent-filter>
159-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:106:13-108:29
160                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
160-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:17-79
160-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:107:25-76
161            </intent-filter>
162        </receiver>
163        <receiver
163-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:110:9-121:20
164            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
164-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:111:13-88
165            android:directBootAware="false"
165-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:112:13-44
166            android:enabled="false"
166-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:113:13-36
167            android:exported="false" >
167-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:114:13-37
168            <intent-filter>
168-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:116:13-120:29
169                <action android:name="android.intent.action.BOOT_COMPLETED" />
169-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:17-79
169-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:117:25-76
170                <action android:name="android.intent.action.TIME_SET" />
170-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:17-73
170-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:118:25-70
171                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
171-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:17-81
171-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:119:25-78
172            </intent-filter>
173        </receiver>
174        <receiver
174-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:122:9-131:20
175            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
175-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:123:13-99
176            android:directBootAware="false"
176-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:124:13-44
177            android:enabled="@bool/enable_system_alarm_service_default"
177-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:125:13-72
178            android:exported="false" >
178-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:126:13-37
179            <intent-filter>
179-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:128:13-130:29
180                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
180-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:17-98
180-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:129:25-95
181            </intent-filter>
182        </receiver>
183        <receiver
183-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:132:9-142:20
184            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
184-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:133:13-78
185            android:directBootAware="false"
185-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:134:13-44
186            android:enabled="true"
186-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:135:13-35
187            android:exported="true"
187-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:136:13-36
188            android:permission="android.permission.DUMP" >
188-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:137:13-57
189            <intent-filter>
189-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:139:13-141:29
190                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
190-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:17-88
190-->[androidx.work:work-runtime:2.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\41a4aba3848461a29f2fe0aac95f12c7\transformed\work-runtime-2.10.1\AndroidManifest.xml:140:25-85
191            </intent-filter>
192        </receiver>
193        <receiver
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
194            android:name="androidx.profileinstaller.ProfileInstallReceiver"
194-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
195            android:directBootAware="false"
195-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
196            android:enabled="true"
196-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
197            android:exported="true"
197-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
198            android:permission="android.permission.DUMP" >
198-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
199            <intent-filter>
199-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
200                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
200-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
200-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
201            </intent-filter>
202            <intent-filter>
202-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
203                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
203-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
203-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
204            </intent-filter>
205            <intent-filter>
205-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
206                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
206-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
206-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
207            </intent-filter>
208            <intent-filter>
208-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
209                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
209-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
209-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8b0efa2aa025eed4750d189e283a659c\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
210            </intent-filter>
211        </receiver>
212
213        <service
213-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
214            android:name="androidx.room.MultiInstanceInvalidationService"
214-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
215            android:directBootAware="true"
215-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
216            android:exported="false" />
216-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a88bdec8a912efc00babe52f942fa09a\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
217    </application>
218
219</manifest>
